'use client';

import React, { useState, useEffect } from 'react';

// Static navigation links to avoid import issues
const staticNavLinks = [
  { href: '/', label: 'Strona główna' },
  { href: '/blog', label: 'Blog' },
  {
    href: '/program',
    label: 'Program',
    submenu: [
      { href: '/program?destination=bali', label: 'Bali - 12 dni' },
      { href: '/program?destination=srilanka', label: 'Sri Lanka - 10 dni' }
    ]
  },
  { href: '/zajecia-online', label: 'Zajęcia Online' },
  { href: '/o-mnie', label: 'O mnie' },
  { href: '/galeria', label: 'Galeria' },
  { href: '/kontakt', label: 'Kontakt' },
];

export default function ClientNavbar() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      // Efekt transparentności
      setIsScrolled(currentScrollY > 50);
      
      // Auto-hide przy przewijaniu w dół
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false);
      } else {
        setIsVisible(true);
      }
      
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  return (
    <header 
      className={`fixed top-0 w-full z-50 transition-all duration-700 ease-out ${
        isVisible ? 'translate-y-0' : '-translate-y-full'
      } ${
        isScrolled 
          ? 'bg-secondary/90 backdrop-blur-xl border-b border-accent/10 shadow-soft' 
          : 'bg-secondary/95 backdrop-blur-md border-b border-accent/5'
      }`}
    >
      <div className="container mx-auto px-6 h-20">
        <div className="flex items-center justify-between h-full">
          {/* Logo - BAKASANA */}
          <a href="/" className="flex items-center group">
            <span className={`text-2xl font-serif font-light tracking-wider transition-all duration-500 ${
              isScrolled ? 'text-accent' : 'text-accent hover:text-primary'
            }`}>
              BAKASANA
            </span>
          </a>

          {/* Nawigacja desktopowa */}
          <nav className="hidden md:flex items-center space-x-10">
            {staticNavLinks.map((link) => (
              <div key={link.href} className="relative group">
                <a
                  href={link.href}
                  className={`text-sm font-light transition-all duration-500 px-4 py-2 tracking-wide relative group ${
                    isScrolled 
                      ? 'text-primary/80 hover:text-accent' 
                      : 'text-primary/70 hover:text-accent'
                  }`}
                >
                  {link.label}
                  {link.submenu && (
                    <span className="ml-2 text-xs transition-transform duration-500 group-hover:rotate-180 opacity-60">▼</span>
                  )}
                  {/* Elegant hover indicator */}
                  <div className="absolute bottom-0 left-1/2 w-0 h-px bg-accent transition-all duration-500 group-hover:w-full group-hover:left-0 opacity-0 group-hover:opacity-100"></div>
                </a>

                {/* Dropdown menu */}
                {link.submenu && (
                  <div className="absolute top-full left-0 mt-3 bg-secondary/98 backdrop-blur-lg shadow-soft rectangular border border-accent/10 p-6 min-w-[240px] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-500 transform translate-y-3 group-hover:translate-y-0 elegant-border">
                    {link.submenu.map((sublink, index) => (
                      <a
                        key={sublink.href}
                        href={sublink.href}
                        className="block text-sm font-light text-primary/70 hover:text-accent hover:bg-accent/5 transition-all duration-300 py-4 px-4 rectangular-subtle tracking-wide relative group/item"
                        style={{ animationDelay: `${index * 100}ms` }}
                      >
                        {sublink.label}
                        <div className="absolute left-0 top-1/2 w-0 h-px bg-accent transition-all duration-300 group-hover/item:w-3 transform -translate-y-1/2 opacity-0 group-hover/item:opacity-100"></div>
                      </a>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </nav>

          {/* Mobilne menu */}
          <div className="md:hidden">
            <details className="relative group">
              <summary className={`cursor-pointer font-light text-sm tracking-wide transition-all duration-300 list-none ${
                isScrolled ? 'text-accent hover:text-primary' : 'text-accent hover:text-primary'
              }`}>
                <span className="flex items-center gap-2">
                  Menu
                  <div className="w-5 h-px bg-accent transition-all duration-300 group-open:rotate-90"></div>
                </span>
              </summary>
              <nav className="absolute right-0 top-full mt-3 bg-secondary/98 backdrop-blur-lg shadow-soft rectangular border border-accent/10 p-6 min-w-[220px] animate-fade-in elegant-border">
                {staticNavLinks.map((link, index) => (
                  <a
                    key={link.href}
                    href={link.href}
                    className="block text-sm font-light text-primary/70 hover:text-accent hover:bg-accent/5 transition-all duration-300 py-3 px-3 rectangular-subtle tracking-wide"
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    {link.label}
                  </a>
                ))}
                <div className="mt-4 pt-4 border-t border-accent/10">
                  <a
                    href="tel:+48606101523"
                    className="block text-sm font-light text-accent py-2 hover:text-primary transition-colors duration-300"
                  >
                    +48 606 101 523
                  </a>
                </div>
              </nav>
            </details>
          </div>
        </div>
      </div>
    </header>
  );
}
