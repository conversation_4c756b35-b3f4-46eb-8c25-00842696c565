'use client';

import React, { useState } from 'react';
import { Plus, Minus } from 'lucide-react';
import OpticalTypography from '@/components/WorldClassDesign/OpticalTypography';

const FAQAccordion = ({ faqs }) => {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="space-y-4">
        {faqs.map((faq, index) => (
          <div
            key={index}
            className="bg-white/60 backdrop-blur-sm border border-gray-200/50 overflow-hidden transition-all duration-300 hover:shadow-sm"
          >
            <button
              onClick={() => toggleFAQ(index)}
              className="w-full px-6 py-5 text-left focus:outline-none focus:ring-2 focus:ring-gray-200 transition-all duration-200"
              aria-expanded={openIndex === index}
            >
              <div className="flex items-center justify-between">
                <OpticalTypography
                  variant="h4"
                  className="text-gray-800 font-light pr-4"
                  preventOrphans={true}
                >
                  {faq.question}
                </OpticalTypography>
                <div className="flex-shrink-0">
                  {openIndex === index ? (
                    <Minus className="w-5 h-5 text-gray-400 transition-transform duration-200" />
                  ) : (
                    <Plus className="w-5 h-5 text-gray-400 transition-transform duration-200" />
                  )}
                </div>
              </div>
            </button>
            
            <div
              className={`overflow-hidden transition-all duration-300 ease-in-out ${
                openIndex === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
              }`}
            >
              <div className="px-6 pb-5">
                <OpticalTypography
                  variant="body"
                  className="text-gray-600 leading-relaxed"
                  preventOrphans={true}
                >
                  {faq.answer}
                </OpticalTypography>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FAQAccordion;