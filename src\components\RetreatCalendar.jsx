'use client';

import React from 'react';
// USUNIĘTE: wszystkie ikony - REMOVED: all icons
// import { Calendar, MapPin, Users, Clock } from 'lucide-react';
// USUNIĘTE: OpticalTypography - u<PERSON><PERSON><PERSON> czystej typografii
// import OpticalTypography from '@/components/WorldClassDesign/OpticalTypography';

const RetreatCalendar = ({ retreats }) => {
  const handleBooking = (retreatId) => {
    // Redirect to booking form or external booking system
    window.location.href = `/rezerwacja?retreat=${retreatId}`;
  };

  // USUNIĘTE: ikony pogody - REMOVED: weather icons
  // Tylko tekst, bez emoji i kolorów
  const getBaliFeason = (startDate) => {
    const month = startDate.includes('czerwca') ? 6 :
                  startDate.includes('lipca') ? 7 :
                  startDate.includes('września') ? 9 : 0;

    if (month >= 4 && month <= 10) {
      return { season: 'Pora sucha' }; // USUNIĘTE: icon, color
    } else {
      return { season: 'Pora deszczowa' }; // USUNIĘTE: icon, color
    }
  };

  // Funkcja określająca czy retreat jest bestsellerem
  const isBestseller = (retreat) => {
    return retreat.participants >= retreat.maxParticipants * 0.7;
  };

  // ARCYDZIEŁO - Święta balijskie i wydarzenia kulturalne
  const getBaliHolidays = (startDate, endDate) => {
    const holidays = [
      { name: 'Nyepi', dates: ['marzec'], icon: '🕉️', description: 'Dzień Ciszy' },
      { name: 'Galungan', dates: ['kwiecień', 'październik'], icon: '🎋', description: 'Święto Dobra' },
      { name: 'Kuningan', dates: ['kwiecień', 'październik'], icon: '🌸', description: 'Dzień Przodków' },
      { name: 'Purnama', dates: ['każdy miesiąc'], icon: '🌕', description: 'Pełnia Księżyca' },
      { name: 'Saraswati', dates: ['luty'], icon: '📚', description: 'Święto Wiedzy' },
      { name: 'Melasti', dates: ['marzec'], icon: '🌊', description: 'Oczyszczenie' }
    ];

    const relevantHolidays = holidays.filter(holiday => {
      if (holiday.dates.includes('każdy miesiąc')) return true;
      return holiday.dates.some(month =>
        startDate.includes(month) || endDate.includes(month)
      );
    });

    return relevantHolidays.slice(0, 2); // Maksymalnie 2 święta
  };

  // USUNIĘTE: ikony aktywności - REMOVED: activity icons
  // Tylko tekst, bez emoji
  const getActivityLabels = (retreat) => {
    const labels = [];

    if (retreat.id.includes('ubud')) {
      labels.push('Medytacja', 'Tarasy ryżowe');
    } else if (retreat.id.includes('gili')) {
      labels.push('Snorkeling', 'Wyspa');
    } else if (retreat.id.includes('canggu')) {
      labels.push('Surfing', 'Zachody słońca');
    }

    return labels;
  };

  return (
    <div className="section">
      {retreats.map((retreat, index) => (
        <article
          key={retreat.id}
          className="retreat-card"
        >
            {/* MINIMALISTYCZNY HEADER - tylko typografia */}
            <div className="retreat-info">
              {/* Typ retreatu - TYLKO tekst */}
              <p className="section-subtitle" style={{ marginBottom: '15px', textAlign: 'left' }}>
                {retreat.type}
              </p>

              {/* USUNIĘTE: wszystkie badges, ikony, animacje */}
              {/* Bestseller info - TYLKO tekst jeśli potrzebny */}
              {isBestseller(retreat) && (
                <p style={{
                  font: '14px "Helvetica Neue", "Helvetica", sans-serif',
                  fontWeight: 300,
                  color: '#6B6B6B',
                  marginBottom: '10px',
                  textTransform: 'uppercase',
                  letterSpacing: '0.1em'
                }}>
                  Popularny wybór
                </p>
              )}
            </div>

            {/* Aktywności - TYLKO tekst, BEZ ikon */}
            <div style={{ marginBottom: '20px' }}>
              {getActivityLabels(retreat).map((label, idx) => (
                <span key={idx} style={{
                  font: '14px "Helvetica Neue", "Helvetica", sans-serif',
                  fontWeight: 300,
                  color: '#6B6B6B',
                  marginRight: '20px',
                  textTransform: 'uppercase',
                  letterSpacing: '0.1em'
                }}>
                  {label}
                </span>
              ))}
            </div>

            {/* Title */}
            <OpticalTypography
              variant="h3"
              className="text-gray-800 font-light mb-3"
              preventOrphans={true}
            >
              {retreat.title}
            </OpticalTypography>

            {/* ARCYDZIEŁO - Dates z balijskimi elementami */}
            <div className="space-y-3 mb-6">
              <div className="flex items-center text-gray-600">
                <Clock className="w-4 h-4 mr-2 text-amber-600" />
                <span className="text-sm font-medium">
                  {retreat.startDate} - {retreat.endDate}
                </span>
              </div>

              {/* Pora roku na Bali - ulepszony */}
              <div className="flex items-center text-gray-600 bg-gradient-to-r from-blue-50/30 to-yellow-50/30 px-3 py-2 rounded-lg border border-amber-200/20">
                <span className="text-lg mr-2">{getBaliFeason(retreat.startDate).icon}</span>
                <span className={`text-sm font-medium ${getBaliFeason(retreat.startDate).color}`}>
                  {getBaliFeason(retreat.startDate).season}
                </span>
              </div>

              {/* Święta balijskie */}
              {getBaliHolidays(retreat.startDate, retreat.endDate).map((holiday, idx) => (
                <div key={idx} className="flex items-center text-gray-600 bg-amber-50/30 px-3 py-2 rounded-lg border border-amber-200/20">
                  <span className="text-sm mr-2">{holiday.icon}</span>
                  <div>
                    <span className="text-sm font-medium text-amber-700">{holiday.name}</span>
                    <span className="text-xs text-amber-600 ml-2">({holiday.description})</span>
                  </div>
                </div>
              ))}

              <div className="flex items-center text-gray-600">
                <MapPin className="w-4 h-4 mr-2 text-amber-600" />
                <span className="text-sm">{retreat.location}</span>
              </div>

              <div className="flex items-center text-gray-600">
                <Users className="w-4 h-4 mr-2 text-amber-600" />
                <span className="text-sm">
                  {retreat.participants} {retreat.maxParticipants ? `/ ${retreat.maxParticipants}` : ''} osób
                </span>
                {/* Progress bar */}
                <div className="ml-3 flex-1 max-w-20">
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div
                      className="bg-gradient-to-r from-amber-400 to-orange-400 h-1.5 rounded-full transition-all duration-300"
                      style={{ width: `${(retreat.participants / retreat.maxParticipants) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Price */}
            <div className="mb-4">
              <OpticalTypography
                variant="h4"
                className="text-gray-800 font-light"
              >
                {retreat.price}
              </OpticalTypography>
              {retreat.originalPrice && (
                <OpticalTypography
                  variant="caption"
                  className="text-gray-400 line-through ml-2"
                >
                  {retreat.originalPrice}
                </OpticalTypography>
              )}
            </div>

            {/* Description */}
            <OpticalTypography
              variant="body"
              className="text-gray-600 text-sm leading-relaxed mb-6"
              preventOrphans={true}
            >
              {retreat.description}
            </OpticalTypography>

            {/* CTA Button */}
            <button
              onClick={() => handleBooking(retreat.id)}
              disabled={!retreat.available}
              className={`w-full py-3 px-4 text-sm font-light tracking-wider uppercase transition-all duration-300 ${
                retreat.available
                  ? 'bg-transparent text-gray-800 border border-gray-400 hover:bg-gray-100 hover:border-gray-500'
                  : 'bg-gray-100 text-gray-400 border border-gray-200 cursor-not-allowed'
              }`}
            >
              {retreat.available ? 'Zarezerwuj miejsce' : 'Brak miejsc'}
            </button>

            {/* Status badge */}
            {retreat.status && (
              <div className="mt-3 text-center">
                <span className={`inline-block px-3 py-1 text-xs font-light rounded-full ${
                  retreat.status === 'early-bird' 
                    ? 'bg-green-100 text-green-700' 
                    : retreat.status === 'filling-fast'
                    ? 'bg-orange-100 text-orange-700'
                    : 'bg-gray-100 text-gray-700'
                }`}>
                  {retreat.status === 'early-bird' && 'Promocja wczesna'}
                  {retreat.status === 'filling-fast' && 'Szybko się zapełnia'}
                  {retreat.status === 'confirmed' && 'Potwierdzone'}
                </span>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default RetreatCalendar;