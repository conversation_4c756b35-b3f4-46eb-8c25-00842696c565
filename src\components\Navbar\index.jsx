'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Menu, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { navigationLinks } from '@/data/navigationLinks';

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const pathname = usePathname();
  const clickTimeoutRef = useRef(null);

  // Hydratacja fix
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Zamknij menu po zmianie strony
  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  // Cleanup timeout przy unmount
  useEffect(() => {
    return () => {
      if (clickTimeoutRef.current) {
        clearTimeout(clickTimeoutRef.current);
      }
    };
  }, []);

  const navLinks = navigationLinks;

  // Poprawiona funkcja z debouncingiem
  const handleMenuToggle = () => {
    if (!isMounted || clickTimeoutRef.current) return;
    
    clickTimeoutRef.current = setTimeout(() => {
      setIsOpen(prev => !prev);
      clickTimeoutRef.current = null;
    }, 100);
  };

  return (
    <nav
      className={`navbar ${scrolled ? 'scrolled' : ''}`}
      suppressHydrationWarning
    >
      <div className="nav-content">
          {/* Logo BAKASANA - ultra minimalistyczny */}
          <Link href="/" className="flex items-center">
            <span className="text-2xl font-serif font-light tracking-wide text-primary">
              BAKASANA
            </span>
          </Link>

          {/* Nawigacja desktopowa - ultra minimalna, inline */}
          <div className="hidden lg:flex items-center gap-12">
            <Link href="/" className="nav-link">
              Strona główna
            </Link>
            <Link href="/program" className="nav-link">
              Retreaty
            </Link>
            <Link href="/o-mnie" className="nav-link">
              O mnie
            </Link>
            <Link href="/kontakt" className="nav-link">
              Kontakt
            </Link>
          </div>

          {/* Przycisk mobilnego menu */}
          <button
            className="lg:hidden text-primary"
            onClick={handleMenuToggle}
            aria-label={isOpen ? 'Zamknij menu' : 'Otwórz menu'}
            suppressHydrationWarning
          >
            {isMounted && isOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobilne menu */}
      {isMounted && (
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="lg:hidden bg-white/95 backdrop-blur-md overflow-hidden border-t border-primary/10"
            >
              <nav className="max-w-7xl mx-auto px-8 py-6 flex flex-col space-y-4">
                <Link href="/" className="text-base py-3 block font-light text-primary/70 hover:text-primary transition-colors" onClick={() => setIsOpen(false)}>
                  Strona główna
                </Link>
                <Link href="/program" className="text-base py-3 block font-light text-primary/70 hover:text-primary transition-colors" onClick={() => setIsOpen(false)}>
                  Retreaty
                </Link>
                <Link href="/o-mnie" className="text-base py-3 block font-light text-primary/70 hover:text-primary transition-colors" onClick={() => setIsOpen(false)}>
                  O mnie
                </Link>
                <Link href="/kontakt" className="text-base py-3 block font-light text-primary/70 hover:text-primary transition-colors" onClick={() => setIsOpen(false)}>
                  Kontakt
                </Link>
              </nav>
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </nav>
  );
}