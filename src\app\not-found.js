'use client';

import Link from 'next/link';
import OpticalTypography from '@/components/WorldClassDesign/OpticalTypography';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-stone-50 flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* Balijska ikona */}
        <div className="mb-8">
          <div className="text-8xl mb-4 animate-bounce" style={{ animationDuration: '2s' }}>
            🐒
          </div>
          <div className="text-4xl text-amber-600/60 breathing-lotus">
            🌿
          </div>
        </div>
        
        {/* Nagłówek z balijskim humorem */}
        <OpticalTypography
          variant="h1"
          className="text-gray-800 mb-6 font-light"
          style={{ fontSize: 'clamp(2rem, 5vw, 3rem)' }}
        >
          Zgubiłeś się jak w dżungli Ubud?
        </OpticalTypography>
        
        <OpticalTypography
          variant="body"
          className="text-gray-600 mb-8 text-lg leading-relaxed"
          preventOrphans={true}
        >
          Nie martw się! Nawet najlepsi przewodnicy czasami gubią szlak. <br />
          Małpa z Monkey Forest pokazuje Ci drogę powrotną.
        </OpticalTypography>
        
        {/* Om separator */}
        <div className="text-center py-8">
          <div className="inline-block text-4xl text-amber-600/30 breathing-om">
            ॐ
          </div>
        </div>
        
        {/* Przyciski powrotu */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/"
            className="inline-flex items-center px-8 py-4 bg-transparent text-gray-800 border-2 border-amber-200/80 text-sm font-light tracking-widest uppercase transition-all duration-300 hover:bg-amber-200/10 hover:border-amber-200 cursor-lotus"
            style={{
              borderRadius: '0',
              boxShadow: '0 2px 8px rgba(139, 69, 19, 0.08)'
            }}
          >
            <span className="mr-2">🏠</span>
            Wróć do domu
          </Link>
          
          <Link
            href="/retreaty"
            className="inline-flex items-center px-8 py-4 bg-transparent text-gray-800 border-2 border-amber-200/80 text-sm font-light tracking-widest uppercase transition-all duration-300 hover:bg-amber-200/10 hover:border-amber-200 cursor-lotus"
            style={{
              borderRadius: '0',
              boxShadow: '0 2px 8px rgba(139, 69, 19, 0.08)'
            }}
          >
            <span className="mr-2">🧘‍♀️</span>
            Znajdź swój retreat
          </Link>
        </div>
        
        {/* Balijskie motto */}
        <div className="mt-12 p-6 bg-gradient-to-r from-amber-50/30 to-orange-50/30 border-l-4 border-amber-200/40 relative">
          <div className="absolute -top-2 -left-2 text-3xl text-amber-600/20">❝</div>
          <OpticalTypography
            variant="body"
            className="text-gray-700 font-light italic"
            style={{
              fontFamily: 'Georgia, serif',
              letterSpacing: '0.02em'
            }}
          >
            "Każda zagubiona ścieżka prowadzi do nowego odkrycia"
          </OpticalTypography>
          <div className="text-right mt-2">
            <span className="text-amber-700 text-sm font-light">~ Balijska mądrość</span>
          </div>
        </div>
      </div>
    </div>
  );
}