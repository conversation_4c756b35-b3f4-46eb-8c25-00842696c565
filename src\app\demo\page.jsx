'use client';

import React, { useState } from 'react';
import WorldClassProvider from '@/components/WorldClassDesign/WorldClassProvider';
import OpticalTypography from '@/components/WorldClassDesign/OpticalTypography';
import MagneticButton from '@/components/WorldClassDesign/MagneticButton';
import StaggeredReveal from '@/components/WorldClassDesign/StaggeredReveal';
import MicroAnimations from '@/components/WorldClassDesign/MicroAnimations';
import PremiumLoader from '@/components/WorldClassDesign/PremiumLoader';
import NoiseTexture from '@/components/WorldClassDesign/NoiseTexture';
import { useWorldClass } from '@/components/WorldClassDesign/WorldClassProvider';

const { 
  BreathingAnimation, 
  MagneticHover, 
  SmoothReveal, 
  FloatingAnimation,
  RippleEffect,
  ShimmerLoading,
  PulseAnimation 
} = MicroAnimations;

/**
 * 🎪 DEMO PAGE - TOP 1% DESIGN SHOWCASE
 * Prezentuje wszystkie premium funkcje
 */

const DemoSection = ({ title, description, children }) => (
  <section className="premium-spacing border-b border-premium-border">
    <div className="premium-container">
      <SmoothReveal delay={0} offset={50}>
        <OpticalTypography
          variant="h2"
          opticalAlign={true}
          preventOrphans={true}
          className="text-temple mb-4"
        >
          {title}
        </OpticalTypography>
        <OpticalTypography
          variant="body"
          preventOrphans={true}
          className="text-wood-light/80 mb-8 max-w-2xl"
        >
          {description}
        </OpticalTypography>
      </SmoothReveal>
      <div className="premium-grid">
        {children}
      </div>
    </div>
  </section>
);

const ControlPanel = () => {
  const { features, toggleFeature, performanceMetrics } = useWorldClass();

  return (
    <div className="fixed top-4 right-4 z-50 glass-effect rounded-lg p-4 max-w-sm">
      <OpticalTypography variant="h3" className="text-temple mb-4">
        Control Panel
      </OpticalTypography>
      
      <div className="space-y-3">
        {Object.entries(features).map(([key, value]) => (
          <label key={key} className="flex items-center justify-between">
            <span className="text-sm text-temple capitalize">
              {key.replace(/([A-Z])/g, ' $1').trim()}
            </span>
            <input
              type="checkbox"
              checked={value}
              onChange={() => toggleFeature(key)}
              className="ml-2"
            />
          </label>
        ))}
      </div>

      <div className="mt-4 pt-4 border-t border-temple/20">
        <div className="text-xs text-temple/70 space-y-1">
          <div>FPS: {performanceMetrics.fps}</div>
          <div>Load: {Math.round(performanceMetrics.loadTime)}ms</div>
          <div>Memory: {Math.round(performanceMetrics.memory)}MB</div>
        </div>
      </div>
    </div>
  );
};

const DemoPage = () => {
  const [isLoading, setIsLoading] = useState(false);

  const handleLoadingDemo = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 3000);
  };

  return (
    <WorldClassProvider>
      <div className="relative bg-gradient-to-b from-rice via-sand-light/50 to-ocean/10 noise-texture">
        <NoiseTexture opacity={0.02} />
        
        <ControlPanel />

        {/* Hero Section */}
        <section className="min-h-screen flex items-center justify-center relative">
          <div className="text-center premium-container">
            <StaggeredReveal delay={100}>
              <OpticalTypography
                variant="display"
                opticalAlign={true}
                manualKerning={true}
                preventOrphans={true}
                className="text-temple mb-6"
              >
                World Class Design
              </OpticalTypography>
              
              <OpticalTypography
                variant="body"
                preventOrphans={true}
                className="text-wood-light/80 mb-8 max-w-2xl mx-auto"
              >
                Poznaj najlepsze funkcje designerskie na poziomie TOP 1%. 
                Inspirowane przez Apple, Linear.app, Stripe i najlepsze SaaS na świecie.
              </OpticalTypography>
              
              <div className="premium-flex justify-center flex-wrap">
                <RippleEffect>
                  <MagneticButton
                    magneticStrength={0.2}
                    className="px-8 py-4 bg-temple text-rice rounded-lg font-medium hover:bg-temple/90 transition-colors"
                  >
                    Magnetic Button
                  </MagneticButton>
                </RippleEffect>
                
                <MagneticButton
                  onClick={handleLoadingDemo}
                  magneticStrength={0.15}
                  className="px-8 py-4 bg-rice text-temple border border-temple/20 rounded-lg font-medium hover:bg-rice/80 transition-colors"
                >
                  Test Loading
                </MagneticButton>
              </div>
            </StaggeredReveal>
          </div>
        </section>

        {/* Typography Section */}
        <DemoSection
          title="Premium Typography"
          description="Optyczne wyrównanie, manual kerning, hanging punctuation i smart quotes"
        >
          <StaggeredReveal delay={50}>
            <div className="space-y-6">
              <OpticalTypography
                variant="h1"
                opticalAlign={true}
                manualKerning={true}
                className="text-temple"
              >
                "Perfect Typography"
              </OpticalTypography>
              
              <OpticalTypography
                variant="body"
                preventOrphans={true}
                hangingPunctuation={true}
                className="text-wood-light leading-relaxed"
              >
                "To jest przykład zaawansowanej typografii z hanging punctuation, 
                manual kerning i optical alignment. Zobacz jak perfekcyjnie wyrównany 
                jest tekst oraz jak 'smart quotes' zastępują zwykłe cudzysłowy."
              </OpticalTypography>
              
              <OpticalTypography
                variant="caption"
                className="text-temple/70 uppercase tracking-wider"
              >
                Perfect kerning: AV, Wa, To, Ya, LT
              </OpticalTypography>
            </div>
          </StaggeredReveal>
        </DemoSection>

        {/* Animations Section */}
        <DemoSection
          title="Micro Animations"
          description="Subtelne animacje na poziomie Apple i Linear.app"
        >
          <StaggeredReveal delay={75}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="glass-effect p-6 rounded-lg text-center">
                <BreathingAnimation intensity={0.03} duration={3000}>
                  <div className="w-16 h-16 bg-temple/20 rounded-full mx-auto mb-4" />
                </BreathingAnimation>
                <OpticalTypography variant="caption" className="text-temple">
                  Breathing Animation
                </OpticalTypography>
              </div>
              
              <div className="glass-effect p-6 rounded-lg text-center">
                <FloatingAnimation amplitude={12} duration={4000}>
                  <div className="w-16 h-16 bg-temple/20 rounded-full mx-auto mb-4" />
                </FloatingAnimation>
                <OpticalTypography variant="caption" className="text-temple">
                  Floating Animation
                </OpticalTypography>
              </div>
              
              <div className="glass-effect p-6 rounded-lg text-center">
                <PulseAnimation scale={1.1} duration={2000}>
                  <div className="w-16 h-16 bg-temple/20 rounded-full mx-auto mb-4" />
                </PulseAnimation>
                <OpticalTypography variant="caption" className="text-temple">
                  Pulse Animation
                </OpticalTypography>
              </div>
              
              <div className="glass-effect p-6 rounded-lg text-center">
                <MagneticHover strength={0.3}>
                  <div className="w-16 h-16 bg-temple/20 rounded-full mx-auto mb-4" />
                </MagneticHover>
                <OpticalTypography variant="caption" className="text-temple">
                  Magnetic Hover
                </OpticalTypography>
              </div>
              
              <div className="glass-effect p-6 rounded-lg text-center">
                <RippleEffect>
                  <div className="w-16 h-16 bg-temple/20 rounded-full mx-auto mb-4 cursor-pointer" />
                </RippleEffect>
                <OpticalTypography variant="caption" className="text-temple">
                  Ripple Effect (kliknij)
                </OpticalTypography>
              </div>
              
              <div className="glass-effect p-6 rounded-lg text-center">
                <ShimmerLoading width="64px" height="64px" borderRadius="50%" />
                <OpticalTypography variant="caption" className="text-temple mt-4">
                  Shimmer Loading
                </OpticalTypography>
              </div>
            </div>
          </StaggeredReveal>
        </DemoSection>

        {/* Loading States */}
        <DemoSection
          title="Premium Loading States"
          description="Eleganckie loadery w różnych stylach"
        >
          <StaggeredReveal delay={100}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="glass-effect p-6 rounded-lg text-center">
                <PremiumLoader isLoading={true} style="minimal" size="medium" />
                <OpticalTypography variant="caption" className="text-temple mt-4">
                  Minimal
                </OpticalTypography>
              </div>
              
              <div className="glass-effect p-6 rounded-lg text-center">
                <PremiumLoader isLoading={true} style="dots" size="medium" />
                <OpticalTypography variant="caption" className="text-temple mt-4">
                  Dots
                </OpticalTypography>
              </div>
              
              <div className="glass-effect p-6 rounded-lg text-center">
                <PremiumLoader isLoading={true} style="breathing" size="medium" />
                <OpticalTypography variant="caption" className="text-temple mt-4">
                  Breathing
                </OpticalTypography>
              </div>
              
              <div className="glass-effect p-6 rounded-lg text-center">
                <PremiumLoader isLoading={true} style="elegant" size="medium" />
                <OpticalTypography variant="caption" className="text-temple mt-4">
                  Elegant
                </OpticalTypography>
              </div>
            </div>
          </StaggeredReveal>
        </DemoSection>

        {/* Visual Effects */}
        <DemoSection
          title="Visual Effects"
          description="Zaawansowane efekty wizualne i tekstury"
        >
          <StaggeredReveal delay={75}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="glass-effect p-8 rounded-lg relative overflow-hidden">
                <NoiseTexture opacity={0.05} />
                <OpticalTypography variant="h3" className="text-temple mb-4">
                  Noise Texture
                </OpticalTypography>
                <OpticalTypography variant="body" className="text-wood-light/80">
                  Subtelna tekstura szumu dla "invisible luxury"
                </OpticalTypography>
              </div>
              
              <div className="glass-effect-dark p-8 rounded-lg text-white">
                <OpticalTypography variant="h3" className="mb-4">
                  Glass Effect
                </OpticalTypography>
                <OpticalTypography variant="body" className="text-white/80">
                  Zaawansowany efekt szkła z backdrop-filter
                </OpticalTypography>
              </div>
            </div>
          </StaggeredReveal>
        </DemoSection>

        {/* Interactive Elements */}
        <DemoSection
          title="Interactive Elements"
          description="Elementy interaktywne z premium feedback"
        >
          <StaggeredReveal delay={50}>
            <div className="space-y-6">
              <div className="premium-flex flex-wrap">
                <button className="premium-clickable px-6 py-3 bg-temple text-rice rounded-lg font-medium shadow-premium hover:shadow-premium-hover">
                  Premium Button
                </button>
                <button className="premium-clickable px-6 py-3 bg-rice text-temple border border-premium-border rounded-lg font-medium shadow-premium hover:shadow-premium-hover">
                  Secondary Button
                </button>
                <button className="premium-clickable px-6 py-3 gradient-premium text-white rounded-lg font-medium shadow-premium hover:shadow-premium-hover">
                  Gradient Button
                </button>
              </div>
              
              <div className="glass-effect p-6 rounded-lg">
                <input
                  type="text"
                  placeholder="Premium Input Field"
                  className="w-full px-4 py-3 border border-premium-border rounded-lg focus-premium bg-white/50 backdrop-blur-sm"
                />
              </div>
            </div>
          </StaggeredReveal>
        </DemoSection>

        {/* Performance Indicators */}
        <section className="premium-spacing-small">
          <div className="premium-container text-center">
            <OpticalTypography variant="caption" className="text-temple/60">
              Wszystkie animacje respektują prefers-reduced-motion
            </OpticalTypography>
          </div>
        </section>

        {/* Loading Overlay */}
        {isLoading && (
          <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center">
            <div className="glass-effect p-8 rounded-lg text-center">
              <PremiumLoader 
                isLoading={true} 
                style="elegant" 
                size="large" 
                className="mb-4"
              />
              <OpticalTypography variant="body" className="text-temple">
                Loading demo...
              </OpticalTypography>
            </div>
          </div>
        )}
      </div>
    </WorldClassProvider>
  );
};

export default DemoPage;