// src/app/layout.jsx
import React from 'react';
import './globals.css';
import { Inter, Playfair_Display } from 'next/font/google';
import ClientNavbar from '../components/Navbar/ClientNavbar';
import ServerFooter from '../components/Footer/ServerFooter';
import { generateMetadata as generateSEOMetadata, generateStructuredData } from './metadata';
import {
  generateOrganizationSchema,
  generateWebsiteSchema,
  generateServiceSchema,
  generateLocalBusinessSchema
} from '../lib/structuredData';
import CriticalCSS from '../components/CriticalCSS';
import ClientResourcePreloader from '../components/ClientResourcePreloader';
import AsyncCSS from '../components/AsyncCSS';
import { ClientAnalytics } from '../components/ClientAnalytics';
import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/react';
import SmoothScroll from '../components/SmoothScroll';
import CookieConsentBanner from '../components/CookieConsent';
import PWAInstaller from '../components/PWAInstaller';
import WebVitalsMonitor from '../components/WebVitalsMonitor';
import AOSProvider from '../components/AOSProvider';

// BAKASANA Fonty - eleganckie i cienkie dla premium wellness
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
  weight: ['300', '400', '500', '600'],
  display: 'swap'
});

const playfairDisplay = Playfair_Display({
  subsets: ['latin'],
  variable: '--font-serif',
  weight: ['400', '500', '600'], // Playfair Display nie ma wagi 300
  display: 'swap'
});

// BAKASANA - Domyślne wartości dla metadata
const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://yourdomain.com';
const siteName = process.env.NEXT_PUBLIC_SITE_NAME || 'BAKASANA';
const siteDescription = process.env.NEXT_PUBLIC_SITE_DESCRIPTION || 'Odkryj Wewnętrzną Harmonię - Premium retreaty jogowe na Bali i Sri Lance.';

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  viewportFit: 'cover',
  themeColor: '#7C9885' // Szałwiowa zieleń BAKASANA
};

export const metadata = generateSEOMetadata({
  title: 'Bakasana - Retreaty Jogowe Bali & Sri Lanka | Julia Jakubowicz',
  description: '⭐ Ekskluzywne retreaty jogowe na Bali i Sri Lance z doświadczoną fizjoterapeutką. ✓ Małe grupy ✓ Transformacyjne podróże ✓ Od 2500 PLN. Odkryj harmonię ciała i ducha →',
  keywords: [
    'joga Bali retreat 2025',
    'retreat jogi Sri Lanka',
    'wyjazd joga Bali Sri Lanka',
    'warsztaty jogi Ubud Sigiriya',
    'retreat jogi dla początkujących',
    'najlepszy retreat jogi opinie',
    'ile kosztuje wyjazd na jogę',
    'joga i medytacja polska instruktorka',
    'bezpieczny wyjazd joga dla kobiet',
    'ayurveda Sri Lanka joga',
    'Julia Jakubowicz fizjoterapeutka',
    'transformacyjne podróże jogowe'
  ],
});

export default function RootLayout({ children }) {
  const structuredData = generateStructuredData({
    type: 'TravelAgency',
  });

  return (
    <html lang="pl" className={`${inter.variable} ${playfairDisplay.variable}`}>
      <head>
        <CriticalCSS />

        {/* Balijskie favicon */}
        <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text x=%2250%22 y=%2250%22 style=%22dominant-baseline:middle;text-anchor:middle;font-size:60px;%22>🪷</text></svg>" />
        
        {/* PWA Meta Tags */}
        <link rel="manifest" href="/manifest.json" />
        <meta name="application-name" content="BAKASANA" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="BAKASANA" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#7C9885" />
        <meta name="msapplication-tap-highlight" content="no" />
        <meta name="theme-color" content="#7C9885" />

        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      </head>
      <body className="min-h-screen flex flex-col">
        <AOSProvider>
          <div className="relative">
            <ClientNavbar />
            <main role="main" className="relative flex-grow pt-20">
              <div className="min-h-screen">
                {children}
              </div>
            </main>
            <ServerFooter />
          </div>
        </AOSProvider>
        <AsyncCSS />
        <ClientResourcePreloader />
        <SmoothScroll />
        {process.env.NODE_ENV === 'production' ? (
          <>
            <ClientAnalytics />
            <Analytics />
            <SpeedInsights />
          </>
        ) : (
          <div style={{ display: 'none' }}>
            {/* Analytics disabled in development */}
          </div>
        )}
        <CookieConsentBanner />
        <PWAInstaller />
        <WebVitalsMonitor />
      </body>
    </html>
  );
}