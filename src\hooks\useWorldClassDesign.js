import { useContext } from 'react';
import { WorldClassContext } from '@/components/WorldClassDesign/WorldClassProvider';

/**
 * 🎯 HOOK FOR WORLD CLASS DESIGN - TOP 1% FEATURES
 * Hook do zarządzania wszystkimi premium funkcjami
 */
export const useWorldClassDesign = () => {
  const context = useContext(WorldClassContext);
  
  if (!context) {
    throw new Error('useWorldClassDesign must be used within WorldClassProvider');
  }
  
  return context;
};

export default useWorldClassDesign;