@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@300;400;500&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;500&display=swap');
/* Didot i Helvetica Neue są systemowe - Didot and Helvetica Neue are system fonts */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ==========================================================================
   BAKASANA - Ultra Minimalist Luxury Design System
   Inspired by: Apple, Linear.app, Kinfolk, High-end Spa Resorts
   Philosophy: 80% white space, typography as decoration, zero ornaments
   ========================================================================== */

@layer base {
  :root {
    /* LUXURY COLOR PALETTE - Warm Minimalism */
    --color-primary: 44 44 44;         /* Deep Charcoal (#2C2C2C) */
    --color-secondary: 254 253 248;    /* Warm White (#FEFDF8) */
    --color-accent: 124 152 133;       /* Sage Green (#7C9885) - kept as requested */
    --color-background: 254 253 248;   /* Warm White Background */
    --color-text-light: 102 102 102;   /* Light text (#666) */

    /* PREMIUM TYPOGRAPHY - Editorial Quality */
    --font-sans: 'Source Sans Pro', 'system-ui', '-apple-system', 'BlinkMacSystemFont', sans-serif;
    --font-serif: 'Playfair Display', 'Georgia', 'Times New Roman', serif;
    
    /* ULTRA-SUBTLE SHADOWS - Barely Visible */
    --shadow-soft: 0 1px 3px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 2px 6px rgba(0, 0, 0, 0.08);

    /* PREMIUM TIMING FUNCTIONS */
    --ease-gentle: cubic-bezier(0.23, 1, 0.32, 1);
    --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  }

  * {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    font-family: var(--font-sans);
    background: #FEFDF8; /* ciepła biel - warm white */
    color: #2C2C2C; /* deep charcoal */
    letter-spacing: 0.01em;
    line-height: 1.6;
    overflow-x: hidden; /* wszystko płynie - everything flows */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* ==========================================================================
     TYPOGRAFIA - JEDYNA OZDOBA (TYPOGRAPHY - ONLY DECORATION)
     Typografia jako jedyny element designu, usunięcie wszystkich ozdób
     ========================================================================== */

  /* Nagłówki sekcji - Section headings */
  h1, h2 {
    font: 48px 'Didot', 'Bodoni MT', 'Times New Roman', serif;
    font-weight: 300;
    text-align: center;
    margin-bottom: 20px;
    letter-spacing: 0.05em;
    color: #2C2C2C; /* deep charcoal */
    line-height: 1.1;
  }

  h1 {
    font-size: clamp(48px, 8vw, 72px);
  }

  h2 {
    font-size: clamp(36px, 6vw, 48px);
  }

  h3 {
    font: 24px 'Didot', 'Bodoni MT', 'Times New Roman', serif;
    font-weight: 300;
    color: #2C2C2C;
    margin-bottom: 15px;
    letter-spacing: 0.03em;
    line-height: 1.2;
  }

  h4, h5, h6 {
    font: 18px 'Helvetica Neue', 'Helvetica', sans-serif;
    font-weight: 300;
    color: #2C2C2C;
    margin-bottom: 10px;
    letter-spacing: 0.02em;
    line-height: 1.3;
  }

  /* Podtytuły - Subtitles */
  .section-subtitle {
    font: 16px 'Helvetica Neue', 'Helvetica', sans-serif;
    font-weight: 300;
    text-align: center;
    color: #6B6B6B; /* muted gray */
    margin-bottom: 80px;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    line-height: 1.4;
  }

  /* Tekst główny - Body text */
  p {
    font: 16px/1.8 'Helvetica Neue', 'Helvetica', sans-serif;
    font-weight: 300;
    max-width: 800px;
    margin: 0 auto 20px;
    color: #2C2C2C;
  }

  p:last-child {
    margin-bottom: 0;
  }

  a {
    color: rgb(var(--color-accent));
    text-decoration: none;
    font-weight: 300;
    transition: opacity 0.3s var(--ease-smooth);
  }

  a:hover {
    opacity: 0.7;
  }

  ::selection {
    color: rgb(var(--color-primary));
    background-color: rgb(var(--color-accent) / 0.1);
  }

  img {
    display: block;
    max-width: 100%;
    height: auto;
    image-rendering: -webkit-optimize-contrast;
  }

  /* Minimal fade-in animation */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .fade-in {
    animation: fadeIn 0.8s var(--ease-gentle) forwards;
  }

  /* Scrollbar styling - Minimal */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 9999px;
    background-color: rgb(var(--color-accent) / 0.20);
    transition: background-color 0.3s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgb(var(--color-accent) / 0.40);
  }
}

@layer components {
  /* ==========================================================================
     FUNDAMENTY - ZERO KONTENERÓW PHILOSOPHY
     GŁÓWNA ZASADA - wszystko płynie (everything flows)
     ========================================================================== */

  /* PODSTAWOWE ZASADY - BASIC PRINCIPLES:
     1. BEZ widocznych kontenerów - NO visible containers
     2. Wszystko płynie na 100% szerokości - Everything flows at 100% width
     3. NIGDY: border, background-color, box-shadow na sekcjach
     4. Tylko padding dla oddychania - Only padding for breathing
     5. Ciepła biel #FEFDF8 jako jedyne tło - Warm white as only background
  */

  .flow-section {
    width: 100%;
    padding: 120px 10%; /* oddychająca przestrzeń */
    background: transparent; /* NIGDY kolorowe tło */
    border: none; /* NIGDY granice */
    box-shadow: none; /* NIGDY cienie */
    margin: 0; /* NIGDY marginesy */
  }

  /* ==========================================================================
     HERO - CZYSTA PERFEKCJA (PURE PERFECTION)
     Zdjęcie na CAŁY ekran, tekst centralnie, BEZ kontenerów
     ========================================================================== */

  .hero-section {
    height: 100vh; /* pełny ekran - full screen */
    position: relative;
    width: 100%;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    /* Zdjęcie na CAŁY ekran - Image covers ENTIRE screen */
    background: rgb(var(--color-secondary));
  }

  .hero-content {
    position: relative;
    z-index: 10;
    text-align: center;
    max-width: 64rem;
    margin: 0 auto;
    padding: 0 2rem;
  }

  .hero-title {
    font: 72px 'Didot', 'Bodoni MT', 'Times New Roman', serif;
    letter-spacing: 0.2em; /* rozstrzelone - spaced out */
    font-weight: 300;
    margin-bottom: 2rem;
    line-height: 1.1;
    color: rgb(var(--color-primary));
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); /* subtelny cień dla czytelności */
  }

  .hero-subtitle {
    font: 14px 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
    letter-spacing: 0.3em; /* bardzo rozstrzelone - very spaced */
    text-transform: uppercase;
    margin-bottom: 3rem;
    color: rgb(var(--color-primary) / 0.85);
    font-weight: 300;
    max-width: 36rem;
    margin-left: auto;
    margin-right: auto;
  }

  /* ==========================================================================
     7. BUTTONS - GHOST STYLE ONLY (ULTRA-MINIMALIST LUXURY)
     Exact specifications for luxury retreat website
     ========================================================================== */

  .button {
    display: inline-block;
    padding: 15px 40px;
    border: 1px solid #2C2C2C; /* dark anthracite */
    background: transparent;
    color: #2C2C2C; /* dark anthracite text */
    font: 12px 'Helvetica Neue', sans-serif;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    transition: all 0.3s ease;
    border-radius: 0; /* no rounded corners */
    cursor: pointer;
    text-decoration: none;
  }

  .button:hover {
    background: #2C2C2C; /* dark anthracite */
    color: #FEFDF8; /* warm white */
    /* No additional animations or effects */
  }

  /* Legacy button styles maintained for compatibility */
  .btn-primary {
    padding: 1rem 2rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    background: transparent;
    border: 1px solid rgb(var(--color-primary) / 0.3);
    color: rgb(var(--color-primary));
    transition: all 0.3s var(--ease-smooth);
    font-weight: 300;
    border-radius: 0;
    cursor: pointer;
  }

  .btn-primary:hover {
    background: rgb(var(--color-primary));
    color: rgb(var(--color-secondary));
    border-color: rgb(var(--color-primary));
  }

  .btn-secondary {
    padding: 1rem 2rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    background: transparent;
    border: 1px solid rgb(var(--color-accent) / 0.3);
    color: rgb(var(--color-accent));
    transition: all 0.3s var(--ease-smooth);
    font-weight: 300;
    border-radius: 0;
    cursor: pointer;
  }

  .btn-secondary:hover {
    background: rgb(var(--color-accent));
    color: rgb(var(--color-secondary));
    border-color: rgb(var(--color-accent));
  }

  /* ==========================================================================
     NAWIGACJA - NIEWIDOCZNA (INVISIBLE NAVIGATION)
     Początkowo przezroczysta, blur tylko po scrollu
     ========================================================================== */

  nav, .navbar {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    padding: 30px 10%; /* oddychająca przestrzeń - breathing space */
    /* Początkowo CAŁKOWICIE przezroczysta - Initially COMPLETELY transparent */
    background: transparent;
    border: none; /* NIGDY ramki - NEVER borders */
    box-shadow: none; /* NIGDY cienie - NEVER shadows */
    transition: all 0.6s ease; /* płynne przejście - smooth transition */
  }

  nav.scrolled, .navbar.scrolled {
    /* Po scrollu - TYLKO subtelny blur - After scroll - ONLY subtle blur */
    backdrop-filter: blur(10px);
    background: rgba(254, 253, 248, 0.8); /* ciepła biel z przezroczystością */
    /* NIGDY: cienie, kolory, zmiana wielkości - NEVER: shadows, colors, size changes */
  }

  .nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0; /* bez dodatkowego paddingu - no extra padding */
    max-width: none; /* bez ograniczeń - no limits */
    margin: 0; /* bez marginesów - no margins */
  }

  .nav-link {
    padding: 0.75rem 1.5rem;
    color: rgb(var(--color-primary) / 0.7);
    font-weight: 300;
    transition: opacity 0.3s var(--ease-smooth);
  }

  .nav-link:hover {
    opacity: 0.7;
  }

  .nav-link[aria-current="page"] {
    color: rgb(var(--color-accent));
  }

  /* ==========================================================================
     KARTY RETREATÓW - MINIMALIZM (RETREAT CARDS - MINIMALISM)
     Całkowicie niewidoczne kontenery z tylko treścią widoczną
     ========================================================================== */

  .retreat-card, .card {
    /* BEZ tła - NO background */
    background: transparent;
    /* BEZ ramek - NO borders */
    border: none;
    /* BEZ cieni - NO shadows */
    box-shadow: none;
    padding: 60px 0; /* tylko oddychająca przestrzeń - only breathing space */
    /* NIGDY: hover effects, color changes, visible containers */
    transition: none; /* USUNIĘTE wszystkie efekty - REMOVED all effects */
    overflow: visible;
  }

  .retreat-card:hover, .card:hover {
    /* NIGDY żadnych efektów hover - NEVER any hover effects */
    background: transparent;
    border: none;
    box-shadow: none;
    transform: none;
    opacity: 1;
  }

  .retreat-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    /* ZERO zaokrągleń - ZERO border radius */
    border-radius: 0;
    /* Sharp, editorial-style photography */
    display: block;
  }

  .retreat-info {
    margin-top: 30px;
    /* Tylko typografia - Only typography */
    background: transparent;
    padding: 0;
    border: none;
    box-shadow: none;
  }

  .card-content {
    padding: 2.5rem;
  }

  .card-image {
    position: relative;
    overflow: hidden;
    aspect-ratio: 16/9;
  }

  .card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* ==========================================================================
     8. IMAGES - FULL WIDTH BREAKOUT (EDITORIAL STYLE)
     Images that break out of content flow for maximum impact
     ========================================================================== */

  .full-width-image {
    width: 100vw; /* full viewport width */
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    height: 60vh;
    object-fit: cover;
    margin-top: 100px;
    margin-bottom: 100px;
    border-radius: 0; /* everything must be rectangular */
    display: block;
  }

  /* ==========================================================================
     9. ABSOLUTE DESIGN PROHIBITIONS - ENFORCED
     Strict rules to maintain ultra-minimalist aesthetic
     ========================================================================== */

  /* Ensure no background colors on sections */
  section,
  .section,
  .flow-section,
  .retreat-section,
  .about-section,
  .calendar-section,
  .contact-section {
    background-color: transparent !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
  }

  /* Prohibit colored backgrounds on cards */
  .card,
  .retreat-card,
  .about-card,
  .calendar-card {
    background-color: transparent !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
  }

  /* Ensure all images are rectangular */
  img,
  .image,
  .retreat-image,
  .about-image,
  .hero-image {
    border-radius: 0 !important;
  }

  /* Prohibit bounce or pulse animations */
  @keyframes bounce {
    /* Disabled animation */
    0%, 100% { transform: none; }
  }

  @keyframes pulse {
    /* Disabled animation */
    0%, 100% { transform: none; }
  }

  .bounce,
  .pulse {
    animation: none !important;
  }

  /* ==========================================================================
     10. PAGE FLOW STRUCTURE - SEAMLESS TRANSITIONS
     Sections flow in exact order with minimal separators
     ========================================================================== */

  /* 1. HERO (100vh height) */
  .hero-section {
    height: 100vh;
    min-height: 100vh;
    /* Already defined above */
  }

  /* 2. RETREATS (images + text only) */
  .retreats-section {
    width: 100%;
    padding: 120px 10%;
    background: transparent;
    border: none;
    box-shadow: none;
  }

  /* 3. ABOUT (image + adjacent text) */
  .about-section {
    width: 100%;
    padding: 120px 10%;
    background: transparent;
    border: none;
    box-shadow: none;
  }

  /* 4. CALENDAR (ultra-simple grid) */
  .calendar-section {
    width: 100%;
    padding: 120px 10%;
    background: transparent;
    border: none;
    box-shadow: none;
  }

  /* 5. CONTACT (centered text) */
  .contact-section {
    width: 100%;
    padding: 120px 10%;
    background: transparent;
    border: none;
    box-shadow: none;
    text-align: center;
  }

  /* Thin divider lines between sections (60px x 1px subtle gray) */
  .section-divider-flow {
    width: 60px;
    height: 1px;
    background: #E5E2DD; /* subtle gray */
    margin: 100px auto;
    border: none;
    /* No decorations - pure minimalism */
  }

  /* ==========================================================================
     DESIGN PHILOSOPHY ENFORCEMENT
     Single elegant document-like flow - Kinfolk/Cereal Magazine aesthetic
     ========================================================================== */

  /* Ensure seamless transitions between sections */
  .page-flow {
    width: 100%;
    background: #FEFDF8; /* warm white background only */
    /* No containers, no breaks, pure flow */
  }

  /* Typography as only decoration - reinforced */
  .editorial-text {
    font: 16px/1.8 'Helvetica Neue', 'Helvetica', sans-serif;
    font-weight: 300;
    max-width: 800px;
    margin: 0 auto;
    color: #2C2C2C;
    /* No backgrounds, borders, or decorations */
  }

  /* Ultra-simple calendar grid */
  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 20px;
    max-width: 600px;
    margin: 0 auto;
    /* No backgrounds or borders */
  }

  .calendar-day {
    padding: 15px;
    text-align: center;
    background: transparent;
    border: none;
    font: 14px 'Helvetica Neue', sans-serif;
    color: #2C2C2C;
  }

  /* Centered contact text */
  .contact-content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
  }

  /* White space as integral design element */
  .breathing-space {
    padding: 120px 0;
  }

  /* Images speak for themselves - no decorations */
  .editorial-image {
    width: 100%;
    height: auto;
    border-radius: 0;
    border: none;
    box-shadow: none;
    display: block;
    margin: 60px 0;
  }

  /* Professional spa/architecture magazine aesthetic */
  .magazine-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: 80px;
    max-width: none;
    width: 100%;
  }

  @media (min-width: 1024px) {
    .magazine-layout {
      grid-template-columns: 1fr 1fr;
      gap: 120px;
    }
  }

  /* Ensure nothing visually "stands out" */
  .seamless-content {
    background: transparent;
    border: none;
    box-shadow: none;
    padding: 0;
    margin: 0;
  }
}

@layer utilities {
  /* ULTRA-PREMIUM LAYOUT - Lots of White Space */
  .section-padding {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  @media (min-width: 768px) {
    .section-padding {
      padding-top: 10rem;
      padding-bottom: 10rem;
    }
  }

  /* ZERO KONTENERÓW - usunięte ograniczenia szerokości */
  /* Zastąpione przez .section z padding: 120px 10% */
  .container-unified {
    /* DEPRECATED - używaj .section zamiast tego */
    max-width: none; /* brak ograniczeń - no limits */
    width: 100%; /* pełna szerokość - full width */
    margin: 0; /* bez marginesów - no margins */
    padding: 0 10%; /* tylko boczne oddychanie - only side breathing */
  }

  .section-unified-title {
    text-align: center;
    margin-bottom: 6rem;
  }

  .section-unified-title h2 {
    margin-bottom: 1.5rem;
    color: rgb(var(--color-accent));
  }

  .section-unified-title p {
    font-size: 1.125rem;
    font-weight: 300;
    color: rgb(var(--color-primary) / 0.85);
    max-width: 48rem;
    margin-left: auto;
    margin-right: auto;
  }

  /* PREMIUM GRID SYSTEM */
  .grid-12-col {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1.5rem;
  }

  @media (min-width: 768px) {
    .grid-12-col {
      gap: 2rem;
    }
  }

  .main-content {
    grid-column: span 12;
  }

  @media (min-width: 768px) {
    .main-content {
      grid-column: span 8;
    }
  }

  .sidebar {
    grid-column: span 12;
  }

  @media (min-width: 768px) {
    .sidebar {
      grid-column: span 4;
    }
  }

  /* ==========================================================================
     SEKCJE - PŁYNNE PRZEJŚCIA (FLOWING SECTION TRANSITIONS)
     Każda sekcja płynnie przechodzi w następną bez kontenerów
     ========================================================================== */

  .section {
    width: 100%; /* pełna szerokość - full width */
    padding: 120px 10%; /* oddychająca przestrzeń - breathing space */
    /* NIGDY osobnego tła - NEVER separate backgrounds */
    background: transparent;
    /* NIGDY ramek czy cieni - NEVER borders or shadows */
    border: none;
    box-shadow: none;
    /* BEZ widocznych kontenerów - NO visible containers */
    position: relative;
  }

  @media (min-width: 1024px) {
    .section {
      padding: 120px 10%; /* konsekwentne oddychanie - consistent breathing */
    }
  }

  .section-title {
    text-align: center;
    margin-bottom: 4rem;
  }

  .section-title h2 {
    margin-bottom: 1.5rem;
    color: rgb(var(--color-primary));
  }

  .section-title p {
    max-width: 48rem;
    margin-left: auto;
    margin-right: auto;
    font-size: 1.125rem;
    color: rgb(var(--color-primary) / 0.7);
  }

  /* ==========================================================================
     SEPARATORY - MAKSYMALNIE SUBTELNE (MAXIMALLY SUBTLE SEPARATORS)
     Minimalne przejścia między sekcjami
     ========================================================================== */

  /* Separatory - MAKSYMALNIE subtelne - MAXIMALLY subtle separators */
  .section-divider {
    width: 60px;
    height: 1px;
    background: #E5E2DD; /* bardzo subtelny szary - very subtle gray */
    margin: 100px auto;
    border: none; /* NIGDY ramki - NEVER borders */
    /* TO WSZYSTKO - żadnych ozdób - THAT'S ALL - no decorations */
  }

  /* Zachowana kompatybilność z istniejącym kodem */
  .divider-line {
    width: 60px;
    height: 1px;
    background: #E5E2DD; /* bardzo subtelny szary - very subtle gray */
    margin: 100px auto;
    border: none;
  }

  .section-number {
    position: absolute;
    top: 2rem;
    left: 2rem;
    font-size: clamp(4rem, 8vw, 8rem);
    font-weight: 300;
    color: rgb(var(--color-primary) / 0.02);
    font-family: var(--font-serif);
    line-height: 1;
    z-index: 0;
    pointer-events: none;
  }

  /* UTILITY CLASSES */
  .meta-text {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.2em;
    font-weight: 300;
    color: rgb(var(--color-primary) / 0.6);
  }

  .subtitle {
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.15em;
    color: rgb(var(--color-accent) / 0.7);
    font-weight: 300;
  }

  /* RESPONSIVE DESIGN */
  @media (max-width: 768px) {
    .hero-title {
      font-size: clamp(36px, 8vw, 72px);
      letter-spacing: 0.15em;
    }

    .hero-subtitle {
      font-size: clamp(10px, 2vw, 14px);
      letter-spacing: 0.25em;
    }

    .hero-section {
      min-height: 85vh;
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .section-padding {
      padding-top: 4rem;
      padding-bottom: 4rem;
    }

    .card-content {
      padding: 1.5rem;
    }

    .nav-content {
      padding: 1rem;
    }

    .container-unified {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }

  /* ACCESSIBILITY */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }

    .fade-in {
      animation: none !important;
    }
  }

  /* PRINT STYLES */
  @media print {
    * {
      box-shadow: none !important;
      text-shadow: none !important;
      background: transparent !important;
      color: black !important;
    }

    .navbar,
    .btn-primary,
    .btn-secondary {
      display: none !important;
    }

    body {
      background: white !important;
      color: black !important;
      font-size: 12pt;
      line-height: 1.4;
    }

    a,
    a:visited {
      text-decoration: underline;
      color: #444 !important;
    }

    img {
      max-width: 100% !important;
      page-break-inside: avoid;
    }

    h2, h3 {
      page-break-after: avoid;
    }

    .card {
      border: 1px solid #ddd !important;
      page-break-inside: avoid;
    }
  }
}
