/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ['class'],
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
    './src/app/**/*.{js,jsx,ts,tsx}',
    './src/components/**/*.{js,jsx,ts,tsx}',
    './src/pages/**/*.{js,jsx,ts,tsx}',
  ],
  corePlugins: {
    preflight: true,
  },
  theme: {
    extend: {
      colors: {
        // ULTRA-PREMIUM: TYLKO 4 KOLORY - 60% neutralne, 30% secondary, 10% akcent
        primary: '#333333',        // Ciemnoszary tekst (60%)
        secondary: '#FFFFFF',      // Czysta biel (30%)
        accent: '#7C9885',         // Szałwiowa zieleń (10%)
        background: '#FAFAF8',     // Off-white tło
        
        // USUNIĘTE: wszystkie dodatkowe warianty (muted, accent-warm, accent-light, etc.)
        // Filozofia: Mniej = bardziej premium
      },
      fontFamily: {
        sans: ['var(--font-sans)', 'Source Sans Pro', 'Open Sans', 'system-ui', 'sans-serif'],
        serif: ['var(--font-serif)', 'Playfair Display', 'Cormorant Garamond', 'Georgia', 'serif'],
        display: ['var(--font-serif)', 'Playfair Display', 'Cormorant Garamond', 'Georgia', 'serif'],
      },
      fontSize: {
        // ULTRA-PREMIUM: Większe spacing dla elegancji
        sm: ['0.875rem', { lineHeight: '2.0', letterSpacing: '0.02em' }],
        base: ['1rem', { lineHeight: '2.0' }],        // Line-height: 2.0 dla paragrafów
        lg: ['1.125rem', { lineHeight: '2.0' }],
        xl: ['1.25rem', { lineHeight: '1.8', letterSpacing: '0.02em' }],
        '2xl': ['1.5rem', { lineHeight: '1.7', letterSpacing: '0.02em' }],
        '3xl': ['1.875rem', { lineHeight: '1.6', letterSpacing: '0.02em' }],
        '4xl': ['2.25rem', { lineHeight: '1.5', letterSpacing: '0.02em' }],  // H1: 0.02em spacing
        '5xl': ['3rem', { lineHeight: '1.4', letterSpacing: '0.02em' }],
        '6xl': ['3.75rem', { lineHeight: '1.3', letterSpacing: '0.02em' }],
      },
      spacing: {
        xs: '0.5rem',
        sm: '1rem',
        md: '1.5rem',
        lg: '2rem',
        xl: '3rem',
        '2xl': '4rem',
        '3xl': '6rem',
        '4xl': '8rem',
        '5xl': '10rem',
        '6xl': '12rem',
        '7xl': '15rem',
        '8xl': '20rem',
        '32': '2rem',     // 32px - mobile section padding
        '160': '10rem',   // 160px - desktop section padding (nie 120px)
        '24': '1.5rem',   // 24px - między elementami
        '32': '2rem',     // 32px - między elementami
      },
      height: {
        hero: '100vh',
        'hero-sm': '85vh',
      },
      maxWidth: {
        content: '75rem',   // 1200px - węższy = bardziej ekskluzywny (nie 1280px)
        '6xl': '72rem',
      },
      animation: {
        'fade-in': 'fadeIn 600ms ease-out forwards',  // Wolniejsze (600ms nie 500ms)
        // USUNIĘTE: gentle-hover (translateY) - tylko opacity transitions
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        // USUNIĘTE: gentleHover - żadnych ruchów
      },
      transitionTimingFunction: {
        'ease-gentle': 'cubic-bezier(0.23, 1, 0.32, 1)',
      },
      transitionDuration: {
        gentle: '300ms',
      },
      boxShadow: {
        // ULTRA-PREMIUM: Tylko jeden typ cienia - elegancki
        elegant: '0 1px 2px rgba(0, 0, 0, 0.03)',  // Jeszcze bardziej subtelny
        // USUNIĘTE: soft, medium, subtle - za dużo opcji = chaos
      },
      borderRadius: {
        // ULTRA-PREMIUM: Tylko prostokąty - żadnych zaokrągleń
        none: '0',
        DEFAULT: '0',     // Domyślnie prostokątne
        // USUNIĘTE: wszystkie zaokrąglenia - prostokąty = bardziej luksusowe
      },
      backgroundImage: {
        'gradient-soft': 'linear-gradient(135deg, rgba(250, 250, 248, 0.95), rgba(255, 255, 255, 0.98))',
      },
      // USUNIĘTE: backdropBlur - może spowalniać, nie jest niezbędny dla premium efektu
    },
  },
  plugins: [],
};