'use client';

import React, { useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { blogPosts } from '@/data/blogPosts';
import { ArrowRight, Instagram, Facebook, CalendarCheck } from 'lucide-react';

// MINIMAL COMPONENTS - Ultra clean design
import TestimonialSlider from '@/components/TestimonialSlider';
import FAQAccordion from '@/components/FAQAccordion';
import RetreatCalendar from '@/components/RetreatCalendar';

// SafeIcon component for rendering icons with fallback
const SafeIcon = React.memo(({ Icon, className }) => {
  if (!Icon) return null;
  return <Icon className={className} />;
});
SafeIcon.displayName = 'SafeIcon';

// Ultra Minimal Hero Section

const HeroSection = React.memo(() => {
  const scrollToRetreatSection = useCallback(() => {
    document.getElementById('retreats')?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  }, []);

  return (
    <section className="hero-section">
      <div className="absolute inset-0 w-full h-full">
        <Image
          src="/images/background/bali-hero.webp"
          alt="Bali retreat sanctuary"
          fill
          priority
          className="object-cover"
          sizes="100vw"
        />
        <div className="hero-overlay" />
      </div>

      <div className="hero-content">
        <h1 className="hero-title hero-text-shadow">
          BAKASANA
        </h1>

        <p className="hero-subtitle hero-text-shadow">
          Retreat & Wellness Sanctuary • Bali
        </p>

        <button
          onClick={scrollToRetreatSection}
          className="btn-primary"
          aria-label="Discover our retreats"
        >
          Discover Retreats
        </button>
      </div>
    </section>
  );
});
HeroSection.displayName = 'HeroSection';

// Minimal Card Component
const Card = React.memo(({ title, description, link, imageUrl }) => {
  return (
    <div className="card fade-in">
      {imageUrl && (
        <div className="card-image">
          <Image
            src={imageUrl}
            alt={title}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
      )}
      <div className="card-content">
        <h3 className="mb-4">
          {link ? (
            <Link href={link} className="hover:opacity-70 transition-opacity">
              {title}
            </Link>
          ) : (
            title
          )}
        </h3>
        <p className="mb-6">
          {description}
        </p>
        {link && (
          <Link
            href={link}
            className="inline-flex items-center text-sm font-light hover:opacity-70 transition-opacity"
          >
            Learn More
            <SafeIcon Icon={ArrowRight} className="ml-2 h-4 w-4" />
          </Link>
        )}
      </div>
    </div>
  );
});
Card.displayName = 'Card';

// Minimal Section Divider
const SectionDivider = React.memo(() => {
  return (
    <div className="divider-line" />
  );
});
SectionDivider.displayName = 'SectionDivider';

// Simplified testimonial component - removed as we use TestimonialSlider

const WellnessPage = ({ latestPosts }) => {
  const router = useRouter();
  const posts = useMemo(() => latestPosts || blogPosts.slice(0, 3), [latestPosts]);

  const retreatHighlights = useMemo(
    () => [
      {
        id: 'ubud',
        title: 'Ubud Sanctuary',
        description: 'Seven days of spiritual immersion in the cultural heart of Bali, surrounded by ancient rice terraces and sacred temples.',
        imageUrl: '/images/retreats/ubud.webp'
      },
      {
        id: 'gili-air',
        title: 'Gili Air Paradise',
        description: 'Five days of pure tranquility on a pristine island sanctuary, where time moves slowly and peace comes naturally.',
        imageUrl: '/images/retreats/gili.webp'
      },
      {
        id: 'canggu',
        title: 'Canggu Cliffs',
        description: 'Ten days of transformation with clifftop yoga sessions overlooking the endless ocean and spectacular sunsets.',
        imageUrl: '/images/retreats/canggu.webp'
      },
    ],
    []
  );

  const testimonials = [
    {
      quote: "The most transformative experience of my life. Julia created a space of warmth and safety that allowed me to truly connect with myself.",
      author: "Anna",
      location: "Warsaw"
    },
    {
      quote: "Perfect balance of yoga, culture, and relaxation. I returned from Bali as a completely renewed person.",
      author: "Marek",
      location: "Krakow"
    },
    {
      quote: "The group became like a second family. Each day brought new discoveries and deeper self-understanding.",
      author: "Kasia",
      location: "Gdansk"
    },
    {
      quote: "Bali with Julia isn't just a retreat—it's a true journey to yourself. I returned with new energy and inner peace.",
      author: "Marta",
      location: "Wroclaw"
    }
  ];

  const faqs = [
    {
      question: "Are the retreats suitable for beginners?",
      answer: "Absolutely. Our retreats welcome all levels of experience. Julia guides each session with attention to individual needs and capabilities."
    },
    {
      question: "What is included in the retreat price?",
      answer: "The price includes accommodation, all vegetarian meals, daily yoga and meditation sessions, cultural excursions, and airport transfers."
    },
    {
      question: "When are the next retreat dates?",
      answer: "Our upcoming retreats are scheduled for June and September 2024. Find detailed dates in our calendar section or contact us directly."
    },
    {
      question: "Do I need yoga experience?",
      answer: "No prior experience necessary. We welcome both beginners and advanced practitioners. Everyone finds their perfect practice level."
    },
    {
      question: "How many people participate in retreats?",
      answer: "Our groups are intimate and personal—maximum 12 participants. This ensures individual attention and meaningful connections."
    }
  ];

  const retreats = [
    {
      id: 'ubud-june-2024',
      type: 'Transformational',
      title: 'Ubud Sanctuary',
      startDate: 'June 15',
      endDate: 'June 22',
      location: 'Ubud, Bali',
      participants: 8,
      maxParticipants: 12,
      price: '€2,400',
      originalPrice: '€2,800',
      description: '7-day retreat in the heart of Bali with yoga, meditation, and cultural immersion.',
      available: true,
      status: 'early-bird'
    },
    {
      id: 'gili-air-july-2024',
      type: 'Restorative',
      title: 'Gili Air Paradise',
      startDate: 'July 20',
      endDate: 'July 25',
      location: 'Gili Air, Indonesia',
      participants: 6,
      maxParticipants: 10,
      price: '€1,800',
      description: '5-day retreat on a pristine island sanctuary. Focus on relaxation and renewal.',
      available: true,
      status: 'filling-fast'
    },
    {
      id: 'canggu-september-2024',
      type: 'Intensive',
      title: 'Canggu Cliffs',
      startDate: 'September 10',
      endDate: 'September 20',
      location: 'Canggu, Bali',
      participants: 10,
      maxParticipants: 12,
      price: '€3,200',
      description: '10-day intensive retreat with clifftop yoga and transformational practices.',
      available: true,
      status: 'confirmed'
    }
  ];

  const socialLinks = [
    { id: 'instagram', href: 'https://www.instagram.com/fly_with_bakasana', label: 'Instagram', icon: Instagram },
    { id: 'facebook', href: 'https://www.facebook.com/p/Fly-with-bakasana-100077568306563/', label: 'Facebook', icon: Facebook },
    { id: 'fitssey', href: 'https://app.fitssey.com/Flywithbakasana/frontoffice', label: 'Bookings', icon: CalendarCheck },
  ];

  return (
    <div className="bg-secondary">
      <HeroSection />

      {/* Retreat Highlights Section */}
      <section id="retreats" className="section-padding">
        <div className="container-unified">
          <div className="section-unified-title">
            <h2>Our Retreats</h2>
            <p>
              Discover the transformative power of yoga in Bali's most sacred and beautiful locations.
            </p>
          </div>

          <div className="grid-12-col">
            {retreatHighlights.map((retreat) => (
              <div key={retreat.id} className="main-content">
                <Card
                  title={retreat.title}
                  description={retreat.description}
                  imageUrl={retreat.imageUrl}
                  link={`/program/${retreat.id}`}
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      <SectionDivider />

      {/* Online Classes Section */}
      <section className="section-padding">
        <div className="container-unified">
          <div className="grid-12-col">
            <div className="main-content">
              <h2 className="mb-6">Online Classes</h2>
              <p className="mb-6">
                Join our regular online yoga sessions. All levels welcome, schedule adapted to your lifestyle.
              </p>
              <p className="mb-6">
                Monday - Wednesday - Friday: 7:00 PM<br />
                Saturday: 10:00 AM (extended session)
              </p>
              <Link
                href="/zajecia-online"
                className="btn-secondary"
              >
                Join Online Classes
              </Link>
            </div>
            <div className="sidebar">
              <div className="relative aspect-square overflow-hidden">
                <Image
                  src="/images/profile/omnie-opt.webp"
                  alt="Julia practicing yoga"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 40vw"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Julia Section */}
      <section className="section-padding">
        <div className="container-unified">
          <div className="grid-12-col">
            <div className="sidebar">
              <div className="relative aspect-[3/4] overflow-hidden">
                <Image
                  src="/images/profile/omnie-opt.webp"
                  alt="Julia Jakubowicz - Yoga Instructor"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
              </div>
            </div>
            <div className="main-content">
              <h2 className="mb-8">About Julia</h2>

              <blockquote className="mb-8 p-6 border-l-2 border-accent/20">
                <p className="text-xl font-light italic mb-4">
                  "True practice begins when we step off the mat."
                </p>
                <cite className="text-sm font-light text-accent">
                  — Julia Jakubowicz, Yoga Instructor & Spiritual Guide
                </cite>
              </blockquote>

              <p className="mb-6">
                Certified yoga instructor with a passion for sharing the wisdom of ancient practices.
                Julia began her yoga journey 8 years ago and continues to deepen her understanding.
              </p>
              <p className="mb-6">
                She specializes in yoga for beginners, vinyasa flow, and meditative practices.
                Bali has become her second spiritual home.
              </p>

              <div className="flex flex-wrap gap-3 mt-8">
                {[
                  { name: 'RYT 200' },
                  { name: 'Yin Yoga' },
                  { name: 'Aerial Yoga' },
                  { name: 'Mindfulness MBSR' }
                ].map((cert) => (
                  <div key={cert.name} className="px-4 py-2 border border-accent/20 text-sm font-light">
                    {cert.name}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      <SectionDivider />

      {/* Testimonials Section */}
      <section className="section-padding">
        <div className="container-unified">
          <div className="section-unified-title">
            <h2>What Our Guests Say</h2>
          </div>

          <TestimonialSlider testimonials={testimonials} />
        </div>
      </section>

      <SectionDivider />

      {/* Upcoming Retreats Section */}
      <section className="section-padding">
        <div className="container-unified">
          <div className="section-unified-title">
            <h2>Upcoming Retreats</h2>
            <p>
              Choose the perfect retreat for your journey. All dates and details in one place.
            </p>
          </div>

          <RetreatCalendar retreats={retreats} />
        </div>
      </section>

      <SectionDivider />

      {/* FAQ Section */}
      <section className="section-padding">
        <div className="container-unified">
          <div className="section-unified-title">
            <h2>Frequently Asked Questions</h2>
          </div>

          <FAQAccordion faqs={faqs} />
        </div>
      </section>

      <SectionDivider />

      {/* Contact Section */}
      <section className="section-padding">
        <div className="container-unified">
          <div className="section-unified-title">
            <h2>Get in Touch</h2>
            <p>
              Have questions? Want to learn more? We'd love to hear from you.
            </p>
          </div>

          <div className="flex justify-center space-x-8 mb-12">
            {socialLinks.map((link) => (
              <a
                key={link.id}
                href={link.href}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 hover:opacity-70 transition-opacity"
              >
                <SafeIcon Icon={link.icon} className="w-5 h-5" />
                <span className="text-sm">{link.label}</span>
              </a>
            ))}
          </div>

          <div className="text-center">
            <p className="text-sm font-light italic">
              Namaste
            </p>
          </div>
        </div>
      </section>

      {/* Blog Section */}
      <section className="section-padding">
        <div className="container-unified">
          <div className="section-unified-title">
            <h2>Latest from Our Journal</h2>
            <p>
              Discover inspiring stories, practical insights, and deep reflections from our yoga journeys.
            </p>
          </div>

          <div className="grid-12-col">
            {posts.map((post) => (
              <div key={post.id} className="main-content">
                <Card
                  title={post.title}
                  description={post.excerpt}
                  link={`/blog/${post.slug}`}
                  imageUrl={post.image}
                />
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href="/blog"
              className="btn-primary"
            >
              View All Articles
              <SafeIcon Icon={ArrowRight} className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default WellnessPage;